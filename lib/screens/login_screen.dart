import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:quizimy/screens/apple_sign_in_page.dart';
import 'package:quizimy/screens/forgot_password_page.dart';
import 'package:quizimy/screens/google_sign_in_page.dart';
import 'package:quizimy/screens/home_screen.dart';
import 'dart:io' show Platform;
import 'package:quizimy/screens/register_screen.dart';
import 'package:quizimy/services/auth_service.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _auth = FirebaseAuth.instance;
  final _firestore = FirebaseFirestore.instance;
  final _authService = AuthService();
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  
  // معرفة إذا كان الجهاز يعمل على نظام iOS
  bool get _isIOS => !kIsWeb && Platform.isIOS;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleEmailLogin() async {
    if (_formKey.currentState!.validate()) {
      if (!mounted) return;
      setState(() => _isLoading = true);

      try {
        // استخدام AuthService بدلاً من _auth مباشرة
        final userCredential = await _authService.signInWithEmailAndPassword(
          _emailController.text.trim(),
          _passwordController.text,
        );

        if (!mounted) return;

        // إعادة تحميل بيانات المستخدم للتأكد من حالة التحقق الحالية
        await userCredential.user?.reload();
        final user = _auth.currentUser;

        if (user != null && !user.emailVerified) {
          // إظهار رسالة تنبيه فقط دون تسجيل الخروج
          if (!mounted) return;
          _showSnackBar('يجب تفعيل حسابك من البريد الإلكتروني أولاً', isError: true);
          setState(() => _isLoading = false);
          return;
        }

        if (user != null && user.emailVerified) {
          // تحديث وقت آخر تسجيل دخول
          await _firestore.collection('users').doc(user.uid).update({
            'lastLogin': FieldValue.serverTimestamp(),
          });

          if (!mounted) return;
          _showSnackBar('تم تسجيل الدخول بنجاح');
          
          // تأخير قصير قبل الانتقال
          await Future.delayed(Duration(milliseconds: 500));
          
          if (!mounted) return;
          // توجيه المستخدم مباشرة إلى الصفحة الرئيسية
          Navigator.of(context).pushReplacementNamed('/home');
        }
      } on FirebaseAuthException catch (e) {
        if (!mounted) return;
        String errorMessage = 'خطأ في تسجيل الدخول تأكد من الايميل والرمز السري';
        
        if (e.code == 'user-not-found') {
          errorMessage = 'لم يتم العثور على حساب بهذا البريد الإلكتروني';
        } else if (e.code == 'wrong-password') {
          errorMessage = 'كلمة المرور غير صحيحة';
        } else if (e.code == 'invalid-email') {
          errorMessage = 'البريد الإلكتروني غير صالح';
        } else if (e.code == 'user-disabled') {
          errorMessage = 'هذا الحساب معطل';
        }
        
        _showSnackBar(errorMessage, isError: true);
      } catch (e) {
        if (!mounted) return;
        _showSnackBar('حدث خطأ غير متوقع', isError: true);
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).clearSnackBars(); // إزالة أي Snackbar سابق
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(fontFamily: 'Cairo')),
        backgroundColor: isError ? Colors.red : Color(0xFF30BEA2), // Updated success color
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0FBF8), // Light green background tint
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Center(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: constraints.maxWidth * 0.05,
                  vertical: constraints.maxHeight * 0.05,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 5,
                        blurRadius: 7,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.all(constraints.maxWidth * 0.03),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Replace logo with stylized app name
                        _buildAppName(constraints),
                        SizedBox(height: constraints.maxHeight * 0.02),
                        _buildTitle(constraints),
                        SizedBox(height: constraints.maxHeight * 0.04),
                        _buildTextField(
                          controller: _emailController,
                          hintText: 'أدخل بريدك الإلكتروني',
                          icon: CupertinoIcons.mail,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء إدخال البريد الإلكتروني';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'الرجاء إدخال بريد إلكتروني صحيح';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: constraints.maxHeight * 0.02),
                        _buildPasswordField(),
                        SizedBox(height: constraints.maxHeight * 0.01),
                        _buildForgotPasswordButton(),
                        SizedBox(height: constraints.maxHeight * 0.02),
                        _buildLoginButton(constraints),
                        SizedBox(height: constraints.maxHeight * 0.03),
                        _buildSignUpButton(),
                        SizedBox(height: constraints.maxHeight * 0.01),
                        _buildDivider(),
                        SizedBox(height: constraints.maxHeight * 0.02),
                        // اختيار زر التسجيل باستخدام آبل أو جوجل حسب نوع الجهاز
                        _isIOS 
                            ? _buildAppleLoginButton(constraints) 
                            : _buildGmailLoginButton(constraints),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Add new method to build stylized app name
  Widget _buildAppName(BoxConstraints constraints) {
    return Container(
      margin: EdgeInsets.only(top: 10),
      child: Column(
        children: [
          Text(
            "Quizimy",
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: constraints.maxWidth * 0.1,
              fontWeight: FontWeight.bold,
              color: Color(0xFF30BEA2),
              letterSpacing: 1.2,
              shadows: [
                Shadow(
                  blurRadius: 5.0,
                  color: Color(0xFF30BEA2).withOpacity(0.3),
                  offset: Offset(0, 2),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 5),
            width: constraints.maxWidth * 0.3,
            height: 3,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF30BEA2).withOpacity(0.2),
                  Color(0xFF30BEA2),
                  Color(0xFF30BEA2).withOpacity(0.2),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle(BoxConstraints constraints) {
    return Text(
      'مرحباً بك',
      style: TextStyle(
        fontFamily: 'Cairo',
        fontSize: constraints.maxWidth * 0.06,
        fontWeight: FontWeight.bold,
        color: Color(0xFF30BEA2), // Updated to the green color
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    bool isPassword = false,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: Colors.grey.shade100,
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF30BEA2).withOpacity(0.1), // Updated shadow color
            spreadRadius: 1,
            blurRadius: 3,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        textAlign: TextAlign.right,
        obscureText: isPassword ? !_isPasswordVisible : false,
        validator: validator,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(fontFamily: 'Cairo', color: Colors.grey),
          prefixIcon: Icon(icon, color: Color(0xFF30BEA2)), // Updated icon color
          suffixIcon: isPassword
              ? IconButton(
                  icon: Icon(
                    _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    setState(() {
                      _isPasswordVisible = !_isPasswordVisible;
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        ),
      ),
    );
  }

  Widget _buildPasswordField() {
    return _buildTextField(
      controller: _passwordController,
      hintText: 'أدخل كلمة المرور',
      icon: CupertinoIcons.lock,
      isPassword: true,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'الرجاء إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  Widget _buildForgotPasswordButton() {
    return Align(
      alignment: Alignment.centerLeft,
      child: TextButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ForgotPasswordPage()),
          );
        },
        child: Text(
          'نسيت كلمة المرور؟',
          style: TextStyle(
            fontFamily: 'Cairo',
            color: Color(0xFF30BEA2), // Updated text color
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton(BoxConstraints constraints) {
    return ElevatedButton(
      onPressed: _isLoading ? null : _handleEmailLogin,
      style: ElevatedButton.styleFrom(
        backgroundColor: Color(0xFF30BEA2), // Updated button color
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        padding: EdgeInsets.symmetric(vertical: constraints.maxHeight * 0.015),
        minimumSize: Size(constraints.maxWidth * 0.7, constraints.maxHeight * 0.05),
      ),
      child: _isLoading
          ? CircularProgressIndicator(color: Colors.white)
          : Text(
              'تسجيل الدخول',
              style: TextStyle(
                fontFamily: 'Cairo', 
                fontSize: constraints.maxWidth * 0.035, 
                color: Colors.white
              ),
            ),
    );
  }

  Widget _buildSignUpButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => RegisterScreen()),
            );
          },
          child: Text(
            'إنشاء حساب جديد',
            style: TextStyle(
              fontFamily: 'Cairo',
              color: Color(0xFF30BEA2), // Updated text color
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Text(
          'ليس لديك حساب؟',
          style: TextStyle(
            fontFamily: 'Cairo',
            color: Colors.grey.shade600
          ),
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Row(
      children: [
        Expanded(child: Divider()),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            'أو',
            style: TextStyle(fontFamily: 'Cairo', color: Colors.grey),
          ),
        ),
        Expanded(child: Divider()),
      ],
    );
  }
  
  // زر تسجيل الدخول باستخدام حساب آبل (iOS فقط)
  Widget _buildAppleLoginButton(BoxConstraints constraints) {
    return ElevatedButton.icon(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => AppleSignInPage()),
        );
      },
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.black,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: BorderSide(color: Colors.grey.shade300),
        ),
        padding: EdgeInsets.symmetric(vertical: constraints.maxHeight * 0.01),
        minimumSize: Size(constraints.maxWidth * 0.7, constraints.maxHeight * 0.05),
      ),
      // استخدام صورة آبل من المسار المحدد
      icon: Image.asset('assets/images/apple_logo.png', height: constraints.maxHeight * 0.025),
      label: Text(
        'Apple تسجيل الدخول باستخدام',
        style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.03),
      ),
    );
  }

  Widget _buildGmailLoginButton(BoxConstraints constraints) {
    return ElevatedButton.icon(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => GoogleSignInPage()),
        );
      },
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.black,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: BorderSide(color: Colors.grey.shade300),
        ),
        padding: EdgeInsets.symmetric(vertical: constraints.maxHeight * 0.01),
        minimumSize: Size(constraints.maxWidth * 0.7, constraints.maxHeight * 0.05),
      ),
      icon: Image.asset('assets/images/google_logo.png', height: constraints.maxHeight * 0.025),
      label: Text(
        'Google تسجيل الدخول باستخدام',
        style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.03),
      ),
    );
  }
}